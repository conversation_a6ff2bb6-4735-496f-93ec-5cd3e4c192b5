<template>
  <div class="batch-job-manager">
    <div class="manager-header">
      <div class="header-left">
        <h1>Batch Job Manager</h1>
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>Dashboard</el-breadcrumb-item>
          <el-breadcrumb-item>Batch Jobs</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="createNewJob">
          <el-icon><Plus /></el-icon>
          New Job
        </el-button>
        <el-button @click="saveCurrentJob" :disabled="!currentJob">
          <el-icon><Document /></el-icon>
          Save
        </el-button>
        <el-button @click="runCurrentJob" :disabled="!currentJob">
          <el-icon><VideoPlay /></el-icon>
          Run
        </el-button>
      </div>
    </div>

    <div class="manager-content">
      <!-- Job List Sidebar -->
      <div class="job-list-sidebar">
        <div class="sidebar-header">
          <h3>Jobs</h3>
          <el-input
            v-model="searchQuery"
            placeholder="Search jobs..."
            size="small"
            :prefix-icon="Search"
          />
        </div>
        <div class="job-list">
          <div
            v-for="job in filteredJobs"
            :key="job.id"
            :class="['job-item', { active: currentJob?.id === job.id }]"
            @click="selectJob(job)"
          >
            <div class="job-info">
              <div class="job-name">{{ job.name }}</div>
              <div class="job-description">{{ job.description }}</div>
            </div>
            <div class="job-status">
              <el-tag :type="getStatusType(job.status)" size="small">
                {{ job.status }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Flow Editor -->
      <div class="flow-editor">
        <div class="editor-toolbar">
          <el-button-group>
            <el-button @click="zoomIn">
              <el-icon><ZoomIn /></el-icon>
            </el-button>
            <el-button @click="zoomOut">
              <el-icon><ZoomOut /></el-icon>
            </el-button>
            <el-button @click="fitView">
              <el-icon><FullScreen /></el-icon>
            </el-button>
          </el-button-group>
          <el-divider direction="vertical" />
          <el-button
            @click="deleteSelectedNodes"
            :disabled="selectedNodes.length === 0"
          >
            <el-icon><Delete /></el-icon>
            Delete
          </el-button>
        </div>

        <div class="flow-container">
          <VueFlow
            v-model:nodes="nodes"
            v-model:edges="edges"
            :node-types="nodeTypes"
            @drop="onDrop"
            @dragover="onDragOver"
            @nodes-change="onNodesChange"
            @edges-change="onEdgesChange"
            @node-click="onNodeClick"
            class="vue-flow"
          >
            <Background />
            <Controls />
            <MiniMap />
          </VueFlow>
        </div>
      </div>

      <!-- Node Palette -->
      <NodePalette
        title="Batch Steps"
        category="batch"
        @drag-start="onPaletteDragStart"
      />

      <!-- Properties Panel -->
      <div class="properties-panel" v-if="selectedNode">
        <div class="panel-header">
          <h3>Properties</h3>
          <el-button text @click="selectedNode = null">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
        <div class="panel-content">
          <el-form :model="selectedNode.data" label-position="top">
            <el-form-item label="Name">
              <el-input v-model="selectedNode.data.label" />
            </el-form-item>
            <el-form-item label="Description">
              <el-input
                v-model="selectedNode.data.description"
                type="textarea"
                :rows="3"
              />
            </el-form-item>
            <el-form-item label="Type">
              <el-select v-model="selectedNode.data.type" disabled>
                <el-option label="Reader" value="reader" />
                <el-option label="Processor" value="processor" />
                <el-option label="Writer" value="writer" />
                <el-option label="Tasklet" value="tasklet" />
              </el-select>
            </el-form-item>
            <el-form-item label="Configuration">
              <el-input
                v-model="configJson"
                type="textarea"
                :rows="8"
                placeholder="Enter JSON configuration..."
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { VueFlow, useVueFlow } from "@vue-flow/core";
import { MiniMap } from "@vue-flow/minimap";
import { Controls } from "@vue-flow/controls";
import { Background } from "@vue-flow/background";
import "@vue-flow/core/dist/style.css";
import "@vue-flow/core/dist/theme-default.css";
import "@vue-flow/controls/dist/style.css";
import "@vue-flow/minimap/dist/style.css";
import {
  Plus,
  Document,
  VideoPlay,
  Search,
  ZoomIn,
  ZoomOut,
  FullScreen,
  Delete,
  Close,
} from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";

import BatchStepNode from "../components/nodes/BatchStepNode.vue";
import NodePalette from "../components/common/NodePalette.vue";
import type { BatchJob, FlowNode, FlowEdge } from "../types";

// Vue Flow setup
const { zoomIn, zoomOut, fitView, addNodes, removeNodes } = useVueFlow();

// Node types for Vue Flow
const nodeTypes = {
  batchStep: BatchStepNode,
};

// Reactive data
const searchQuery = ref("");
const currentJob = ref<BatchJob | null>(null);
const selectedNode = ref<FlowNode | null>(null);
const selectedNodes = ref<string[]>([]);
const nodes = ref<FlowNode[]>([]);
const edges = ref<FlowEdge[]>([]);

// Sample jobs data
const jobs = ref<BatchJob[]>([
  {
    id: "1",
    name: "Data Import Job",
    description: "Import data from external sources",
    status: "Running",
    steps: [],
    createdAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-01-15T10:30:00Z",
  },
  {
    id: "2",
    name: "Report Generation",
    description: "Generate daily reports",
    status: "Completed",
    steps: [],
    createdAt: "2024-01-15T09:00:00Z",
    updatedAt: "2024-01-15T09:15:00Z",
  },
]);

// Computed properties
const filteredJobs = computed(() => {
  if (!searchQuery.value) return jobs.value;
  return jobs.value.filter(
    (job) =>
      job.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      job.description.toLowerCase().includes(searchQuery.value.toLowerCase())
  );
});

const configJson = computed({
  get: () => {
    if (!selectedNode.value?.data.config) return "";
    return JSON.stringify(selectedNode.value.data.config, null, 2);
  },
  set: (value: string) => {
    if (!selectedNode.value) return;
    try {
      selectedNode.value.data.config = JSON.parse(value);
    } catch (e) {
      // Invalid JSON, ignore
    }
  },
});

// Methods
const getStatusType = (status: string) => {
  switch (status) {
    case "Running":
      return "warning";
    case "Completed":
      return "success";
    case "Failed":
      return "danger";
    case "Pending":
      return "info";
    default:
      return "";
  }
};

const createNewJob = () => {
  const newJob: BatchJob = {
    id: Date.now().toString(),
    name: "New Job",
    description: "New batch job",
    status: "Pending",
    steps: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
  jobs.value.push(newJob);
  selectJob(newJob);
};

const selectJob = (job: BatchJob) => {
  currentJob.value = job;
  loadJobFlow(job);
};

const loadJobFlow = (job: BatchJob) => {
  // Convert job steps to flow nodes
  nodes.value = job.steps.map((step) => ({
    id: step.id,
    type: "batchStep",
    position: step.position,
    data: {
      label: step.name,
      type: step.type,
      description: step.description,
      config: step.config,
    },
  }));

  // Create edges based on connections
  edges.value = [];
  job.steps.forEach((step) => {
    step.connections.forEach((targetId) => {
      edges.value.push({
        id: `${step.id}-${targetId}`,
        source: step.id,
        target: targetId,
        animated: true,
      });
    });
  });
};

const saveCurrentJob = () => {
  if (!currentJob.value) return;

  // Convert flow nodes back to job steps
  currentJob.value.steps = nodes.value.map((node) => ({
    id: node.id,
    name: node.data.label,
    type: node.data.type as any,
    description: node.data.description || "",
    config: node.data.config || {},
    position: node.position,
    connections: edges.value
      .filter((edge) => edge.source === node.id)
      .map((edge) => edge.target),
  }));

  currentJob.value.updatedAt = new Date().toISOString();

  ElMessage.success("Job saved successfully!");
};

const runCurrentJob = () => {
  if (!currentJob.value) return;

  currentJob.value.status = "Running";
  ElMessage.success("Job started!");
};

const onDrop = (event: DragEvent) => {
  const data = event.dataTransfer?.getData("application/vueflow");
  if (!data) return;

  const nodeType = JSON.parse(data);
  const position = { x: event.offsetX, y: event.offsetY };

  const newNode: FlowNode = {
    id: Date.now().toString(),
    type: "batchStep",
    position,
    data: {
      label: `New ${nodeType.name}`,
      description: `New ${nodeType.name.toLowerCase()} step`,
      config: {},
    },
  };

  addNodes([newNode]);
};

const onDragOver = (event: DragEvent) => {
  event.preventDefault();
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = "move";
  }
};

const onNodesChange = (changes: any[]) => {
  // Handle node changes
};

const onEdgesChange = (changes: any[]) => {
  // Handle edge changes
};

const onNodeClick = (event: any) => {
  selectedNode.value = event.node;
};

const onPaletteDragStart = (event: DragEvent, nodeType: any) => {
  // Handled by NodePalette component
};

const deleteSelectedNodes = () => {
  if (selectedNodes.value.length === 0) return;

  removeNodes(selectedNodes.value);
  selectedNodes.value = [];
  selectedNode.value = null;
};

// Initialize with first job
if (jobs.value.length > 0) {
  selectJob(jobs.value[0]);
}
</script>

<style scoped>
.batch-job-manager {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
}

.header-left h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.manager-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.job-list-sidebar {
  width: 300px;
  background: white;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.sidebar-header h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #303133;
}

.job-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.job-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 4px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.job-item:hover {
  background-color: #f5f7fa;
}

.job-item.active {
  background-color: #ecf5ff;
  border: 1px solid #409eff;
}

.job-info {
  flex: 1;
}

.job-name {
  font-weight: 600;
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.job-description {
  font-size: 12px;
  color: #909399;
}

.flow-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

.editor-toolbar {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  gap: 8px;
}

.flow-container {
  flex: 1;
  position: relative;
}

.vue-flow {
  height: 100%;
}

.properties-panel {
  width: 350px;
  background: white;
  border-left: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.panel-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}
</style>
