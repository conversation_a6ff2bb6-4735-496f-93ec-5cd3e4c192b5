<template>
  <div class="pipeline-manager">
    <div class="manager-header">
      <div class="header-left">
        <h1>CI/CD Pipeline Manager</h1>
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>Dashboard</el-breadcrumb-item>
          <el-breadcrumb-item>Pipelines</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="createNewPipeline">
          <el-icon><Plus /></el-icon>
          New Pipeline
        </el-button>
        <el-button @click="saveCurrentPipeline" :disabled="!currentPipeline">
          <el-icon><Document /></el-icon>
          Save
        </el-button>
        <el-button @click="runCurrentPipeline" :disabled="!currentPipeline">
          <el-icon><VideoPlay /></el-icon>
          Run
        </el-button>
      </div>
    </div>

    <div class="manager-content">
      <!-- Pipeline List Sidebar -->
      <div class="pipeline-list-sidebar">
        <div class="sidebar-header">
          <h3>Pipelines</h3>
          <el-input
            v-model="searchQuery"
            placeholder="Search pipelines..."
            size="small"
            :prefix-icon="Search"
          />
        </div>
        <div class="pipeline-list">
          <div
            v-for="pipeline in filteredPipelines"
            :key="pipeline.id"
            :class="[
              'pipeline-item',
              { active: currentPipeline?.id === pipeline.id },
            ]"
            @click="selectPipeline(pipeline)"
          >
            <div class="pipeline-info">
              <div class="pipeline-name">{{ pipeline.name }}</div>
              <div class="pipeline-description">{{ pipeline.description }}</div>
            </div>
            <div class="pipeline-status">
              <el-tag :type="getStatusType(pipeline.status)" size="small">
                {{ pipeline.status }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Flow Editor -->
      <div class="flow-editor">
        <div class="editor-toolbar">
          <el-button-group>
            <el-button @click="zoomIn">
              <el-icon><ZoomIn /></el-icon>
            </el-button>
            <el-button @click="zoomOut">
              <el-icon><ZoomOut /></el-icon>
            </el-button>
            <el-button @click="fitView">
              <el-icon><FullScreen /></el-icon>
            </el-button>
          </el-button-group>
          <el-divider direction="vertical" />
          <el-button
            @click="deleteSelectedNodes"
            :disabled="selectedNodes.length === 0"
          >
            <el-icon><Delete /></el-icon>
            Delete
          </el-button>
          <el-divider direction="vertical" />
          <el-button @click="toggleLayout">
            <el-icon><Sort /></el-icon>
            Auto Layout
          </el-button>
        </div>

        <div class="flow-container">
          <VueFlow
            v-model:nodes="nodes"
            v-model:edges="edges"
            :node-types="nodeTypes"
            @drop="onDrop"
            @dragover="onDragOver"
            @nodes-change="onNodesChange"
            @edges-change="onEdgesChange"
            @node-click="onNodeClick"
            @connect="onConnect"
            class="vue-flow"
          >
            <Background />
            <Controls />
            <MiniMap />
          </VueFlow>
        </div>
      </div>

      <!-- Node Palette -->
      <NodePalette
        title="Pipeline Stages"
        category="pipeline"
        @drag-start="onPaletteDragStart"
      />

      <!-- Properties Panel -->
      <div class="properties-panel" v-if="selectedNode">
        <div class="panel-header">
          <h3>Stage Properties</h3>
          <el-button text @click="selectedNode = null">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
        <div class="panel-content">
          <el-form :model="selectedNode.data" label-position="top">
            <el-form-item label="Name">
              <el-input v-model="selectedNode.data.label" />
            </el-form-item>
            <el-form-item label="Description">
              <el-input
                v-model="selectedNode.data.description"
                type="textarea"
                :rows="3"
              />
            </el-form-item>
            <el-form-item label="Type">
              <el-select v-model="selectedNode.data.type" disabled>
                <el-option label="Build" value="build" />
                <el-option label="Test" value="test" />
                <el-option label="Deploy" value="deploy" />
                <el-option label="Approval" value="approval" />
                <el-option label="Custom" value="custom" />
              </el-select>
            </el-form-item>
            <el-form-item label="Parallel Execution">
              <el-switch v-model="selectedNode.data.parallel" />
            </el-form-item>
            <el-form-item label="Configuration">
              <el-input
                v-model="configJson"
                type="textarea"
                :rows="8"
                placeholder="Enter JSON configuration..."
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <!-- Pipeline Triggers Dialog -->
    <el-dialog
      v-model="showTriggersDialog"
      title="Pipeline Triggers"
      width="600px"
    >
      <div class="triggers-content">
        <div class="triggers-header">
          <h4>Configure Pipeline Triggers</h4>
          <el-button type="primary" size="small" @click="addTrigger">
            <el-icon><Plus /></el-icon>
            Add Trigger
          </el-button>
        </div>
        <div class="triggers-list">
          <div
            v-for="(trigger, index) in currentPipeline?.triggers || []"
            :key="trigger.id"
            class="trigger-item"
          >
            <el-card>
              <div class="trigger-header">
                <el-select
                  v-model="trigger.type"
                  placeholder="Select trigger type"
                >
                  <el-option label="Manual" value="manual" />
                  <el-option label="Webhook" value="webhook" />
                  <el-option label="Schedule" value="schedule" />
                  <el-option label="Git Push" value="push" />
                </el-select>
                <el-button
                  type="danger"
                  size="small"
                  @click="removeTrigger(index)"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
              <div class="trigger-config" v-if="trigger.type">
                <el-form
                  :model="trigger.config"
                  label-position="top"
                  size="small"
                >
                  <template v-if="trigger.type === 'webhook'">
                    <el-form-item label="Webhook URL">
                      <el-input v-model="trigger.config.url" />
                    </el-form-item>
                    <el-form-item label="Secret">
                      <el-input
                        v-model="trigger.config.secret"
                        type="password"
                      />
                    </el-form-item>
                  </template>
                  <template v-else-if="trigger.type === 'schedule'">
                    <el-form-item label="Cron Expression">
                      <el-input
                        v-model="trigger.config.cron"
                        placeholder="0 0 * * *"
                      />
                    </el-form-item>
                  </template>
                  <template v-else-if="trigger.type === 'push'">
                    <el-form-item label="Repository">
                      <el-input v-model="trigger.config.repository" />
                    </el-form-item>
                    <el-form-item label="Branch">
                      <el-input
                        v-model="trigger.config.branch"
                        placeholder="main"
                      />
                    </el-form-item>
                  </template>
                </el-form>
              </div>
            </el-card>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="showTriggersDialog = false">Cancel</el-button>
        <el-button type="primary" @click="saveTriggersAndClose">Save</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { VueFlow, useVueFlow } from "@vue-flow/core";
import { Background } from "@vue-flow/background";
import { Controls } from "@vue-flow/controls";
import { MiniMap } from "@vue-flow/minimap";
import "@vue-flow/core/dist/style.css";
import "@vue-flow/core/dist/theme-default.css";
import "@vue-flow/controls/dist/style.css";
import "@vue-flow/minimap/dist/style.css";
import {
  Plus,
  Document,
  VideoPlay,
  Search,
  ZoomIn,
  ZoomOut,
  FullScreen,
  Delete,
  Close,
  Sort,
} from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";

import PipelineStageNode from "../components/nodes/PipelineStageNode.vue";
import NodePalette from "../components/common/NodePalette.vue";
import type { Pipeline, FlowNode, FlowEdge, PipelineTrigger } from "../types";

// Vue Flow setup
const { zoomIn, zoomOut, fitView, addNodes, removeNodes } = useVueFlow();

// Node types for Vue Flow
const nodeTypes = {
  pipelineStage: PipelineStageNode,
};

// Reactive data
const searchQuery = ref("");
const currentPipeline = ref<Pipeline | null>(null);
const selectedNode = ref<FlowNode | null>(null);
const selectedNodes = ref<string[]>([]);
const nodes = ref<FlowNode[]>([]);
const edges = ref<FlowEdge[]>([]);
const showTriggersDialog = ref(false);

// Sample pipelines data
const pipelines = ref<Pipeline[]>([
  {
    id: "1",
    name: "Frontend Deploy",
    description: "Deploy frontend application",
    status: "Running",
    stages: [],
    triggers: [],
    createdAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-01-15T11:00:00Z",
  },
  {
    id: "2",
    name: "Backend API",
    description: "Build and deploy backend API",
    status: "Completed",
    stages: [],
    triggers: [],
    createdAt: "2024-01-15T09:00:00Z",
    updatedAt: "2024-01-15T10:45:00Z",
  },
]);

// Computed properties
const filteredPipelines = computed(() => {
  if (!searchQuery.value) return pipelines.value;
  return pipelines.value.filter(
    (pipeline) =>
      pipeline.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      pipeline.description
        .toLowerCase()
        .includes(searchQuery.value.toLowerCase())
  );
});

const configJson = computed({
  get: () => {
    if (!selectedNode.value?.data.config) return "";
    return JSON.stringify(selectedNode.value.data.config, null, 2);
  },
  set: (value: string) => {
    if (!selectedNode.value) return;
    try {
      selectedNode.value.data.config = JSON.parse(value);
    } catch (e) {
      // Invalid JSON, ignore
    }
  },
});

// Methods
const getStatusType = (status: string) => {
  switch (status) {
    case "Running":
      return "warning";
    case "Completed":
      return "success";
    case "Failed":
      return "danger";
    case "Pending":
      return "info";
    default:
      return "";
  }
};

const createNewPipeline = () => {
  const newPipeline: Pipeline = {
    id: Date.now().toString(),
    name: "New Pipeline",
    description: "New CI/CD pipeline",
    status: "Pending",
    stages: [],
    triggers: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
  pipelines.value.push(newPipeline);
  selectPipeline(newPipeline);
};

const selectPipeline = (pipeline: Pipeline) => {
  currentPipeline.value = pipeline;
  loadPipelineFlow(pipeline);
};

const loadPipelineFlow = (pipeline: Pipeline) => {
  // Convert pipeline stages to flow nodes
  nodes.value = pipeline.stages.map((stage) => ({
    id: stage.id,
    type: "pipelineStage",
    position: stage.position,
    data: {
      label: stage.name,
      type: stage.type,
      description: stage.description,
      parallel: stage.parallel,
      config: stage.config,
    },
  }));

  // Create edges based on dependencies
  edges.value = [];
  pipeline.stages.forEach((stage) => {
    stage.dependencies.forEach((depId) => {
      edges.value.push({
        id: `${depId}-${stage.id}`,
        source: depId,
        target: stage.id,
        animated: true,
      });
    });
  });
};

const saveCurrentPipeline = () => {
  if (!currentPipeline.value) return;

  // Convert flow nodes back to pipeline stages
  currentPipeline.value.stages = nodes.value.map((node) => ({
    id: node.id,
    name: node.data.label,
    type: node.data.type as any,
    description: node.data.description || "",
    config: node.data.config || {},
    position: node.position,
    dependencies: edges.value
      .filter((edge) => edge.target === node.id)
      .map((edge) => edge.source),
    parallel: node.data.parallel || false,
  }));

  currentPipeline.value.updatedAt = new Date().toISOString();

  ElMessage.success("Pipeline saved successfully!");
};

const runCurrentPipeline = () => {
  if (!currentPipeline.value) return;

  currentPipeline.value.status = "Running";
  ElMessage.success("Pipeline started!");
};

const onDrop = (event: DragEvent) => {
  const data = event.dataTransfer?.getData("application/vueflow");
  if (!data) return;

  const nodeType = JSON.parse(data);
  const position = { x: event.offsetX, y: event.offsetY };

  const newNode: FlowNode = {
    id: Date.now().toString(),
    type: "pipelineStage",
    position,
    data: {
      label: `New ${nodeType.name}`,
      type: nodeType.type,
      description: `New ${nodeType.name.toLowerCase()} stage`,
      config: {},
      parallel: false,
    },
  };

  addNodes([newNode]);
};

const onDragOver = (event: DragEvent) => {
  event.preventDefault();
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = "move";
  }
};

const onNodesChange = (changes: any[]) => {
  // Handle node changes
};

const onEdgesChange = (changes: any[]) => {
  // Handle edge changes
};

const onNodeClick = (event: any) => {
  selectedNode.value = event.node;
};

const onConnect = (connection: any) => {
  // Add new edge when nodes are connected
  const newEdge: FlowEdge = {
    id: `${connection.source}-${connection.target}`,
    source: connection.source,
    target: connection.target,
    animated: true,
  };

  edges.value.push(newEdge);
};

const onPaletteDragStart = (event: DragEvent, nodeType: any) => {
  // Handled by NodePalette component
};

const deleteSelectedNodes = () => {
  if (selectedNodes.value.length === 0) return;

  removeNodes(selectedNodes.value);
  selectedNodes.value = [];
  selectedNode.value = null;
};

const toggleLayout = () => {
  // Auto-arrange nodes in a vertical flow
  if (nodes.value.length === 0) return;

  const sortedNodes = [...nodes.value].sort((a, b) => {
    // Sort by dependencies (stages with no dependencies first)
    const aDeps = edges.value.filter((e) => e.target === a.id).length;
    const bDeps = edges.value.filter((e) => e.target === b.id).length;
    return aDeps - bDeps;
  });

  sortedNodes.forEach((node, index) => {
    node.position = {
      x: 200 + (index % 3) * 250,
      y: 100 + Math.floor(index / 3) * 150,
    };
  });
};

const addTrigger = () => {
  if (!currentPipeline.value) return;

  const newTrigger: PipelineTrigger = {
    id: Date.now().toString(),
    type: "manual",
    config: {},
  };

  currentPipeline.value.triggers.push(newTrigger);
};

const removeTrigger = (index: number) => {
  if (!currentPipeline.value) return;
  currentPipeline.value.triggers.splice(index, 1);
};

const saveTriggersAndClose = () => {
  showTriggersDialog.value = false;
  ElMessage.success("Triggers saved successfully!");
};

// Initialize with first pipeline
if (pipelines.value.length > 0) {
  selectPipeline(pipelines.value[0]);
}
</script>

<style scoped>
.pipeline-manager {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
}

.header-left h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.manager-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.pipeline-list-sidebar {
  width: 300px;
  background: white;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.sidebar-header h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #303133;
}

.pipeline-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.pipeline-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 4px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.pipeline-item:hover {
  background-color: #f5f7fa;
}

.pipeline-item.active {
  background-color: #ecf5ff;
  border: 1px solid #409eff;
}

.pipeline-info {
  flex: 1;
}

.pipeline-name {
  font-weight: 600;
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.pipeline-description {
  font-size: 12px;
  color: #909399;
}

.flow-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

.editor-toolbar {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  gap: 8px;
}

.flow-container {
  flex: 1;
  position: relative;
}

.vue-flow {
  height: 100%;
}

.properties-panel {
  width: 350px;
  background: white;
  border-left: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.panel-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.triggers-content {
  padding: 16px;
}

.triggers-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.triggers-header h4 {
  margin: 0;
  color: #303133;
}

.triggers-list {
  max-height: 400px;
  overflow-y: auto;
}

.trigger-item {
  margin-bottom: 16px;
}

.trigger-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.trigger-config {
  margin-top: 12px;
}
</style>
